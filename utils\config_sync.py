"""
配置同步模块

实现YAML配置到.env文件的自动同步，确保单一配置源原则，
避免多个配置文件之间的不一致问题。

功能：
1. 从YAML配置文件读取主配置
2. 自动同步关键参数到Node.js的.env文件
3. 配置一致性检查和验证
4. 配置同步日志记录
"""

import os
import re
import time
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any

from utils.logger import get_logger
from utils.config import get_config_manager


class ConfigSyncManager:
    """配置同步管理器"""
    
    def __init__(self):
        """初始化配置同步管理器"""
        self.logger = get_logger("config_sync")
        self.config_manager = get_config_manager()
        
        # 需要同步的.env文件路径
        self.env_files = [
            "core/trading/gmgn_executor/.env"
        ]
        
        # 配置映射关系：YAML路径 → .env变量名
        self.config_mappings = {
            ("trading", "position", "default_amount"): "DEFAULT_BUY_AMOUNT",
            ("trading_executor", "solana", "priority_fee"): "NORMAL_TRADE_FEE",
            ("trading_executor", "solana", "slippage"): "DEFAULT_SLIPPAGE"
        }
        
        # 同步统计
        self.sync_stats = {
            "total_syncs": 0,
            "successful_syncs": 0,
            "failed_syncs": 0,
            "files_updated": 0,
            "last_sync_time": None
        }
        
        self.logger.info("🔄 配置同步管理器初始化完成")
    
    def sync_config_to_env_files(self) -> bool:
        """
        将YAML配置同步到所有.env文件
        
        Returns:
            同步是否成功
        """
        start_time = time.time()
        self.sync_stats["total_syncs"] += 1
        
        try:
            # 读取主配置
            yaml_config = self._load_yaml_config()
            if not yaml_config:
                self.logger.error("❌ 无法读取YAML配置")
                self.sync_stats["failed_syncs"] += 1
                return False
            
            # 提取需要同步的配置值
            sync_values = self._extract_sync_values(yaml_config)
            if not sync_values:
                self.logger.error("❌ 无法提取同步配置值")
                self.sync_stats["failed_syncs"] += 1
                return False
            
            self.logger.info(f"📋 准备同步配置: {sync_values}")
            
            # 同步到所有.env文件
            success_count = 0
            for env_file in self.env_files:
                if self._sync_to_env_file(env_file, sync_values):
                    success_count += 1
                    self.sync_stats["files_updated"] += 1
            
            # 检查同步结果
            if success_count == len(self.env_files):
                self.sync_stats["successful_syncs"] += 1
                self.sync_stats["last_sync_time"] = time.time()
                sync_time = time.time() - start_time
                
                self.logger.info(f"✅ 配置同步完成: {success_count}/{len(self.env_files)} 文件更新 (耗时: {sync_time:.3f}s)")
                return True
            else:
                self.sync_stats["failed_syncs"] += 1
                self.logger.error(f"⚠️ 部分配置同步失败: {success_count}/{len(self.env_files)} 文件更新")
                return False
                
        except Exception as e:
            self.sync_stats["failed_syncs"] += 1
            self.logger.error(f"❌ 配置同步异常: {e}")
            return False
    
    def validate_config_consistency(self) -> Tuple[bool, List[str]]:
        """
        验证配置一致性
        
        Returns:
            (是否一致, 不一致的详情列表)
        """
        try:
            # 读取YAML配置
            yaml_config = self._load_yaml_config()
            if not yaml_config:
                return False, ["无法读取YAML配置"]
            
            yaml_values = self._extract_sync_values(yaml_config)
            inconsistencies = []
            
            # 检查每个.env文件
            for env_file in self.env_files:
                env_values = self._load_env_values(env_file)
                if not env_values:
                    inconsistencies.append(f"无法读取 {env_file}")
                    continue
                
                # 比较配置值
                for env_key, yaml_value in yaml_values.items():
                    env_value = env_values.get(env_key)
                    if env_value is None:
                        inconsistencies.append(f"{env_file}: 缺少 {env_key}")
                    elif str(env_value) != str(yaml_value):
                        inconsistencies.append(
                            f"{env_file}: {env_key} 不一致 (YAML: {yaml_value}, ENV: {env_value})"
                        )
            
            is_consistent = len(inconsistencies) == 0
            
            if is_consistent:
                self.logger.info("✅ 配置一致性检查通过")
            else:
                self.logger.warning(f"⚠️ 发现 {len(inconsistencies)} 个配置不一致问题")
                for issue in inconsistencies:
                    self.logger.warning(f"   - {issue}")
            
            return is_consistent, inconsistencies
            
        except Exception as e:
            self.logger.error(f"❌ 配置一致性检查异常: {e}")
            return False, [f"检查异常: {e}"]
    
    def auto_fix_inconsistencies(self) -> bool:
        """
        自动修复配置不一致问题
        
        Returns:
            修复是否成功
        """
        self.logger.info("🔧 开始自动修复配置不一致问题")
        
        # 先检查一致性
        is_consistent, issues = self.validate_config_consistency()
        
        if is_consistent:
            self.logger.info("✅ 配置已一致，无需修复")
            return True
        
        # 执行同步修复
        self.logger.info(f"🔄 发现 {len(issues)} 个不一致问题，开始自动修复")
        success = self.sync_config_to_env_files()
        
        if success:
            # 再次验证
            is_consistent_after, _ = self.validate_config_consistency()
            if is_consistent_after:
                self.logger.info("✅ 配置不一致问题修复成功")
                return True
            else:
                self.logger.error("❌ 配置修复后仍存在不一致问题")
                return False
        else:
            self.logger.error("❌ 配置同步失败，无法修复不一致问题")
            return False
    
    def _load_yaml_config(self) -> Optional[Dict[str, Any]]:
        """加载YAML配置"""
        try:
            trading_config = self.config_manager.get_trading_config()
            return trading_config
        except Exception as e:
            self.logger.error(f"❌ 加载YAML配置失败: {e}")
            return None
    
    def _extract_sync_values(self, yaml_config: Dict[str, Any]) -> Dict[str, Any]:
        """从YAML配置中提取需要同步的值"""
        sync_values = {}
        
        try:
            for yaml_path, env_key in self.config_mappings.items():
                # 按路径获取配置值
                value = yaml_config
                for key in yaml_path:
                    value = value.get(key, {})
                
                if value and not isinstance(value, dict):
                    sync_values[env_key] = value
                else:
                    self.logger.warning(f"⚠️ 配置路径 {'.'.join(yaml_path)} 未找到有效值")
            
            return sync_values
            
        except Exception as e:
            self.logger.error(f"❌ 提取同步配置值失败: {e}")
            return {}
    
    def _sync_to_env_file(self, env_file_path: str, sync_values: Dict[str, Any]) -> bool:
        """同步配置到指定的.env文件"""
        try:
            # 检查文件是否存在
            if not os.path.exists(env_file_path):
                self.logger.error(f"❌ .env文件不存在: {env_file_path}")
                return False
            
            # 读取现有内容
            with open(env_file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 更新配置值
            updated_content = content
            updated_count = 0
            
            for env_key, new_value in sync_values.items():
                # 使用正则表达式匹配和替换
                pattern = rf'^{re.escape(env_key)}=.*$'
                replacement = f'{env_key}={new_value}'
                
                if re.search(pattern, updated_content, re.MULTILINE):
                    updated_content = re.sub(pattern, replacement, updated_content, flags=re.MULTILINE)
                    updated_count += 1
                else:
                    self.logger.warning(f"⚠️ 在 {env_file_path} 中未找到 {env_key}")
            
            # 写回文件
            if updated_count > 0:
                with open(env_file_path, 'w', encoding='utf-8') as f:
                    f.write(updated_content)
                
                self.logger.info(f"✅ 更新 {env_file_path}: {updated_count} 个配置项")
                return True
            else:
                self.logger.warning(f"⚠️ {env_file_path}: 没有配置项需要更新")
                return True
                
        except Exception as e:
            self.logger.error(f"❌ 同步到 {env_file_path} 失败: {e}")
            return False
    
    def _load_env_values(self, env_file_path: str) -> Dict[str, str]:
        """从.env文件加载配置值"""
        env_values = {}
        
        try:
            if not os.path.exists(env_file_path):
                return env_values
            
            with open(env_file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        env_values[key.strip()] = value.strip()
            
            return env_values
            
        except Exception as e:
            self.logger.error(f"❌ 读取 {env_file_path} 失败: {e}")
            return {}
    
    def get_sync_statistics(self) -> Dict[str, Any]:
        """获取同步统计信息"""
        return {
            "stats": self.sync_stats.copy(),
            "success_rate": (
                self.sync_stats["successful_syncs"] / max(self.sync_stats["total_syncs"], 1)
            ) * 100,
            "env_files": self.env_files,
            "config_mappings": {
                ".".join(k): v for k, v in self.config_mappings.items()
            }
        }
    
    def reset_statistics(self) -> None:
        """重置同步统计"""
        self.sync_stats = {
            "total_syncs": 0,
            "successful_syncs": 0,
            "failed_syncs": 0,
            "files_updated": 0,
            "last_sync_time": None
        }
        self.logger.info("📊 同步统计已重置")


# 全局配置同步管理器实例
_config_sync_manager = None


def get_config_sync_manager() -> ConfigSyncManager:
    """获取配置同步管理器实例"""
    global _config_sync_manager
    if _config_sync_manager is None:
        _config_sync_manager = ConfigSyncManager()
    return _config_sync_manager


def sync_config_on_startup() -> bool:
    """系统启动时的配置同步"""
    manager = get_config_sync_manager()
    
    # 先检查一致性
    is_consistent, issues = manager.validate_config_consistency()
    
    if not is_consistent:
        # 自动修复不一致问题
        return manager.auto_fix_inconsistencies()
    else:
        # 配置已一致，但仍然执行一次同步确保最新
        return manager.sync_config_to_env_files()


def validate_config_on_startup() -> bool:
    """系统启动时的配置验证"""
    manager = get_config_sync_manager()
    is_consistent, issues = manager.validate_config_consistency()
    
    if not is_consistent:
        manager.logger.warning("⚠️ 启动时发现配置不一致问题，建议运行配置同步")
        for issue in issues:
            manager.logger.warning(f"   - {issue}")
    
    return is_consistent
