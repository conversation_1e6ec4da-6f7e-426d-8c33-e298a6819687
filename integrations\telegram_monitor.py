"""
电报群监控模块

实现Telegram Bot API集成，支持群消息监控、信号验证和700秒验证窗口管理。
"""

import asyncio
import re
import time
from typing import Dict, List, Optional, Set, Callable
from dataclasses import dataclass
from telethon import TelegramClient, events
from utils.logger import get_logger


@dataclass
class TelegramSignal:
    """电报信号数据结构"""
    token_address: str
    message_text: str
    chat_id: str
    user_id: int
    username: Optional[str]
    timestamp: float
    verification_keywords: List[str]


class TelegramMonitor:
    """电报群监控器"""
    
    def __init__(self, config: Dict):
        """
        初始化电报监控器
        
        Args:
            config: 电报配置参数
        """
        self.config = config
        self.logger = get_logger("telegram_monitor")
        
        # 配置参数
        self.api_id = config.get("api_id", 24702520)
        self.api_hash = config.get("api_hash", "7b1f880deb5998f36a079cfdbd097534")
        self.channel_ids = config.get("channel_ids", [-1001914959004, -1002050218130])
        self.enabled = config.get("enabled", False)

        # CPW验证配置
        self.min_cpw_value = 278
        self.exclude_keywords = ["Total Call", "Main Calls"]
        
        # 状态管理
        self.verified_tokens: Set[str] = set()
        self.pending_signals: Dict[str, TelegramSignal] = {}
        self.verification_callbacks: List[Callable] = []
        self.client = None
        self.is_running = False
        self.original_stderr = None  # 保存原始stderr

        # 验证窗口管理
        self.verification_window = 700  # 700秒验证窗口
        self.cleanup_interval = 60  # 60秒清理一次过期信号
        
        self.logger.info("📱 电报监控器初始化完成")
        if not self.enabled:
            self.logger.warning("⚠️ 电报监控已禁用")
        elif not self.api_id or not self.api_hash:
            self.logger.warning("⚠️ 电报API配置不完整，监控将无法启动")
    
    def add_verification_callback(self, callback: Callable[[str], None]) -> None:
        """
        添加验证回调函数
        
        Args:
            callback: 验证成功时的回调函数，参数为token_address
        """
        self.verification_callbacks.append(callback)
    
    async def start_monitoring(self) -> bool:
        """
        启动电报监控

        Returns:
            是否启动成功
        """
        if not self.enabled:
            self.logger.info("📱 电报监控已禁用，跳过启动")
            return False

        if not self.api_id or not self.api_hash:
            self.logger.error("❌ 电报API配置不完整，无法启动监控")
            return False

        if self.is_running:
            self.logger.warning("⚠️ 电报监控已在运行中")
            return True

        try:
            # 配置代理（如果需要）
            proxy = None
            if self.config.get("use_proxy", False):
                proxy = {
                    'proxy_type': 'http',
                    'addr': '127.0.0.1',
                    'port': 7890
                }

            # 创建Telethon客户端，禁用自动重连和错误处理
            self.client = TelegramClient(
                'telegram_monitor',
                self.api_id,
                self.api_hash,
                proxy=proxy,
                auto_reconnect=False,  # 禁用自动重连
                catch_up=False  # 禁用消息追赶
            )

            # 注册消息处理器
            @self.client.on(events.NewMessage(chats=self.channel_ids))
            async def message_handler(event):
                try:
                    await self._handle_message(event)
                except Exception as e:
                    # 忽略Telegram协议解析错误，避免终端输出混乱
                    if "TypeNotFoundError" in str(e) or "telethon.errors" in str(e):
                        self.logger.debug(f"忽略Telegram协议错误: {e}")
                    else:
                        self.logger.error(f"消息处理异常: {e}")

            # 启动客户端（支持交互式身份验证）
            self.logger.info("🔐 正在连接电报...")
            self.logger.info("📱 如果是首次使用，请按提示输入手机号和验证码")

            await self.client.start(
                phone=lambda: input("请输入手机号（包含国家代码，如+86）: "),
                code_callback=lambda: input("请输入验证码: "),
                password=lambda: input("请输入两步验证密码（如果有）: ") or None
            )

            # 设置全局错误处理器，完全禁用Telethon的错误输出
            import logging
            import sys

            # 禁用所有Telethon相关的日志输出
            telethon_logger = logging.getLogger('telethon')
            telethon_logger.setLevel(logging.CRITICAL)  # 只显示CRITICAL级别
            telethon_logger.disabled = True  # 完全禁用

            # 重定向stderr来捕获未处理的错误
            class ErrorSuppressor:
                def write(self, text):
                    # 只抑制Telethon相关的错误
                    if 'telethon' in text.lower() or 'typenotfounderror' in text.lower() or 'unhandled error while processing msgs' in text.lower():
                        return
                    sys.__stderr__.write(text)
                def flush(self):
                    sys.__stderr__.flush()

            # 暂时重定向stderr
            self.original_stderr = sys.stderr
            sys.stderr = ErrorSuppressor()

            # 启动清理任务
            asyncio.create_task(self._cleanup_expired_signals())

            self.is_running = True
            self.logger.info("✅ 电报监控启动成功")
            self.logger.info(f"📱 监控频道: {self.channel_ids}")
            self.logger.info(f"🎯 CPW阈值: {self.min_cpw_value}")

            return True

        except Exception as e:
            self.logger.error(f"❌ 启动电报监控失败: {e}")
            self.logger.info("💡 提示：首次使用需要进行身份验证")
            return False
    
    async def stop_monitoring(self) -> None:
        """停止电报监控"""
        if not self.is_running:
            return

        try:
            if self.client:
                await self.client.disconnect()

            self.is_running = False
            self.logger.info("🛑 电报监控已停止")

            # 恢复原始stderr
            import sys
            if self.original_stderr and hasattr(sys.stderr, '__class__') and sys.stderr.__class__.__name__ == 'ErrorSuppressor':
                sys.stderr = self.original_stderr

        except Exception as e:
            self.logger.error(f"停止电报监控失败: {e}")
    
    async def _handle_message(self, event) -> None:
        """
        处理电报消息

        Args:
            event: Telethon消息事件
        """
        try:
            message_text = event.message.message
            if not message_text:
                return

            chat_id = event.chat_id
            sender_id = event.sender_id

            self.logger.debug(f"📱 收到消息: {message_text[:50]}...")

            # 检查排除关键词
            if any(keyword in message_text for keyword in self.exclude_keywords):
                self.logger.debug(f"⏭️ 跳过消息（包含排除关键词）")
                return

            # 提取代币地址
            token_addresses = self._extract_token_addresses(message_text)

            # 提取CPW值
            cpw_value = self._extract_cpw_value(message_text)

            if token_addresses and cpw_value is not None:
                # 检查CPW是否满足阈值
                if cpw_value >= self.min_cpw_value:
                    for token_address in token_addresses:
                        await self._verify_token_signal(token_address, message_text, cpw_value)
                else:
                    self.logger.debug(f"📊 CPW值不足: {cpw_value} < {self.min_cpw_value}")

        except Exception as e:
            self.logger.error(f"处理电报消息失败: {e}")
    
    def _extract_token_addresses(self, text: str) -> List[str]:
        """
        从消息中提取代币地址
        
        Args:
            text: 消息文本
            
        Returns:
            代币地址列表
        """
        # Solana地址正则表达式 (Base58编码，32-44字符，移除边界限制以处理Unicode字符)
        pattern = r'[1-9A-HJ-NP-Za-km-z]{32,44}'
        addresses = re.findall(pattern, text)
        
        # 过滤掉明显不是代币地址的字符串
        valid_addresses = []
        for addr in addresses:
            # 基本验证：长度和字符集
            if 32 <= len(addr) <= 44 and not addr.isdigit():
                valid_addresses.append(addr)
        
        return valid_addresses
    
    def _extract_cpw_value(self, text: str) -> Optional[float]:
        """
        从消息中提取CPW值

        Args:
            text: 消息文本

        Returns:
            CPW值，如果未找到则返回None
        """
        try:
            # 基于实际API返回内容的CPW格式
            cpw_patterns = [
                # 格式1: | CPW: 数值 (实际格式)
                r'\|\s*CPW:\s*(\d+(?:\.\d+)?)',
                # 格式2: |⚡️Avg CPW: 数值 (实际格式)
                r'\|⚡️Avg CPW:\s*(\d+(?:\.\d+)?)',
                # 格式3: Avg CPW: 数值 (通用格式)
                r'Avg CPW:\s*(\d+(?:\.\d+)?)',
                # 格式4: CPW: 数值 (简化格式)
                r'CPW:\s*(\d+(?:\.\d+)?)',
                # 格式5: | Avg CPW: 数值
                r'\|\s*Avg CPW:\s*(\d+(?:\.\d+)?)',
                # 格式6: [**Avg CPW**](链接)**:** 数值 (原格式保留)
                r'\[\*\*Avg CPW\*\*\]\([^)]+\)\*\*:\*\*\s*(\d+(?:\.\d+)?)',
            ]

            for pattern in cpw_patterns:
                match = re.search(pattern, text, re.IGNORECASE)
                if match:
                    cpw_value = float(match.group(1))
                    self.logger.debug(f"📊 提取CPW值: {cpw_value} (使用模式: {pattern})")
                    return cpw_value

            # 如果没有匹配到，记录消息内容用于调试
            self.logger.debug(f"🔍 未找到CPW值，消息内容: {text[:200]}...")
            return None

        except Exception as e:
            self.logger.error(f"提取CPW值失败: {e}")
            return None
    
    async def _verify_token_signal(self, token_address: str, message_text: str, cpw_value: float) -> None:
        """
        验证代币信号

        Args:
            token_address: 代币地址
            message_text: 消息文本
            cpw_value: CPW值
        """
        # 前置检查：只验证在待验证列表中的代币（20秒延迟保护）
        if token_address not in self.pending_signals:
            self.logger.debug(f"⏳ 代币 {token_address[:8]} 不在待验证列表中，跳过验证（等待20秒延迟）")
            return

        if token_address in self.verified_tokens:
            self.logger.debug(f"📱 代币 {token_address[:8]} 已验证过")
            return

        self.verified_tokens.add(token_address)

        # 从待验证列表中移除
        if token_address in self.pending_signals:
            del self.pending_signals[token_address]

        self.logger.info(f"✅ 电报信号验证成功: {token_address[:8]}")
        self.logger.info(f"📊 CPW值: {cpw_value} (阈值: {self.min_cpw_value})")

        # 调用验证回调
        for callback in self.verification_callbacks:
            try:
                await callback(token_address)
            except Exception as e:
                self.logger.error(f"验证回调执行失败: {e}")
    
    async def add_pending_signal(self, token_address: str) -> None:
        """
        添加待验证信号（延迟20秒以等待策略积累数据点）

        Args:
            token_address: 代币地址
        """
        # 20秒延迟，等待策略积累45个数据点
        self.logger.info(f"⏳ 延迟20秒后添加待验证信号: {token_address[:8]}...")
        await asyncio.sleep(20)

        # 重复检查防止竞态条件
        if token_address not in self.verified_tokens and token_address not in self.pending_signals:
            signal = TelegramSignal(
                token_address=token_address,
                message_text="",
                chat_id="",
                user_id=0,
                username=None,
                timestamp=time.time(),
                verification_keywords=[]
            )
            self.pending_signals[token_address] = signal
            self.logger.info(f"📝 添加待验证信号: {token_address[:8]}...")
    
    async def _cleanup_expired_signals(self) -> None:
        """清理过期的待验证信号"""
        while self.is_running:
            try:
                current_time = time.time()
                expired_tokens = []
                
                for token_address, signal in self.pending_signals.items():
                    if current_time - signal.timestamp > self.verification_window:
                        expired_tokens.append(token_address)
                
                # 移除过期信号
                for token_address in expired_tokens:
                    del self.pending_signals[token_address]
                    self.logger.info(f"⏰ 清理过期信号: {token_address[:8]} (超过{self.verification_window}秒)")
                
                await asyncio.sleep(self.cleanup_interval)
                
            except Exception as e:
                self.logger.error(f"清理过期信号失败: {e}")
                await asyncio.sleep(self.cleanup_interval)
    
    def is_token_verified(self, token_address: str) -> bool:
        """
        检查代币是否已验证
        
        Args:
            token_address: 代币地址
            
        Returns:
            是否已验证
        """
        return token_address in self.verified_tokens
    
    def get_pending_signals_count(self) -> int:
        """获取待验证信号数量"""
        return len(self.pending_signals)
    
    def get_verified_tokens_count(self) -> int:
        """获取已验证代币数量"""
        return len(self.verified_tokens)
    
    def get_status(self) -> Dict:
        """获取监控状态"""
        return {
            "enabled": self.enabled,
            "running": self.is_running,
            "channel_ids": self.channel_ids,
            "pending_signals": self.get_pending_signals_count(),
            "verified_tokens": self.get_verified_tokens_count(),
            "verification_window": self.verification_window,
            "min_cpw_value": self.min_cpw_value,
            "exclude_keywords": self.exclude_keywords
        }
