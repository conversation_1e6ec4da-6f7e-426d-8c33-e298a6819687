"""
价格监控模块

集成现有的chainstack监控脚本，实现：
- 0.4秒高频数据采集
- 价格历史数据管理
- 多数据源支持
- 异步价格推送
"""

import asyncio
import subprocess
import json
import time
import threading
import requests
from datetime import datetime
from typing import Dict, List, Optional, Callable, Any
from pathlib import Path
import tempfile
import os
from dataclasses import dataclass

from .interfaces import IPriceMonitor, PriceData
from utils.logger import get_logger
from utils.config import get_config_manager

@dataclass
class PoolReserveInfo:
    """流动性池储备金信息（保持原脚本结构）"""
    pool_address: str
    token_reserve_account: str
    sol_reserve_account: str
    token_mint: str
    cached_at: str

@dataclass
class PriceSnapshot:
    """价格快照（保持原脚本结构）"""
    price_sol: float
    price_usd: float
    timestamp: str
    token_reserve: float
    sol_reserve: float
    source: str


class ChainStackPriceMonitor(IPriceMonitor):
    """基于ChainStack的价格监控器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化价格监控器（集成原脚本的所有配置）

        Args:
            config: 价格监控配置
        """
        self.config = config
        self.logger = get_logger("price_monitor")

        # 从配置文件读取Chainstack配置
        chainstack_config = config.get("chainstack", {})
        self.rpc_url = chainstack_config.get("rpc_url", "https://nd-550-325-789.p2pify.com/ce0e1dc0a635321ca624699a5bb62937")
        self.wss_url = chainstack_config.get("wss_url", "wss://ws-nd-550-325-789.p2pify.com/ce0e1dc0a635321ca624699a5bb62937")
        self.use_proxy = chainstack_config.get("use_proxy", False)
        self.proxy_port = chainstack_config.get("proxy_port", 7890)
        self.rpc_timeout = chainstack_config.get("timeout", 6.0)  # 从配置读取超时时间

        # 初始化HTTP会话（保持原脚本逻辑）
        self.session = requests.Session()
        if self.use_proxy:
            proxies = {
                'http': f'http://127.0.0.1:{self.proxy_port}',
                'https': f'http://127.0.0.1:{self.proxy_port}'
            }
            self.session.proxies.update(proxies)
            # 优化连接池配置，避免连接冲突
            self.session.trust_env = False  # 忽略环境变量中的代理设置
            adapter = requests.adapters.HTTPAdapter(
                pool_connections=1,  # 减少连接池大小
                pool_maxsize=2,      # 减少最大连接数
                max_retries=2        # 减少重试次数
            )
            self.session.mount('http://', adapter)
            self.session.mount('https://', adapter)
            self.logger.info(f"已启用代理: 127.0.0.1:{self.proxy_port}")
        else:
            # 明确禁用代理
            self.session.trust_env = False  # 忽略环境变量中的代理设置
            self.session.proxies = {}  # 清空代理设置
            self.logger.info("已禁用代理连接")

        # 原脚本的缓存数据结构
        self.pool_cache: Dict[str, PoolReserveInfo] = {}
        self.price_history: List[PriceSnapshot] = []



        # 监控状态
        self.monitoring_tokens: Dict[str, bool] = {}
        self.price_callbacks: List[Callable[[str, PriceData], None]] = []
        self.price_data: Dict[str, List[PriceData]] = {}
        self.monitor_threads: Dict[str, threading.Thread] = {}
        self.monitor_processes: Dict[str, subprocess.Popen] = {}
        self.latest_reserves: Dict[str, Dict] = {}



        # 临时目录
        self.temp_dir = Path(tempfile.gettempdir()) / "price_monitor"
        self.temp_dir.mkdir(exist_ok=True)

        # 原脚本的性能统计
        self.stats = {
            'total_rpc_calls': 0,
            'successful_rpc_calls': 0,
            'failed_rpc_calls': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'start_time': None
        }

        # SOL价格配置（保持原脚本逻辑）
        self.sol_price_usd = 200.0  # 默认值，启动时会更新
        self.last_sol_price_update = 0

        # 配置参数
        self.update_interval = config.get("update_interval", 0.4)
        self.history_length = config.get("history_length", 100)

        # 外部脚本配置
        external_script_config = config.get("external_script", {})
        script_path_str = external_script_config.get("script_path", "price moniter/chainstack_hybrid_monitor.py")
        self.script_path = Path(script_path_str)

        # 启动时获取一次实时SOL价格（保持原脚本逻辑）
        self._update_sol_price()

        self.logger.info("📊 价格监控器初始化完成")
        self.logger.info(f"   - 更新间隔: {self.update_interval}s")
        self.logger.info(f"   - 历史长度: {self.history_length}")

    def make_chainstack_rpc_request(self, method: str, params: List[Any]) -> Dict[str, Any]:
        """
        发送符合Chainstack格式的RPC请求（保持原脚本逻辑）

        Args:
            method: RPC方法名
            params: 参数列表

        Returns:
            RPC响应结果
        """
        # 严格按照Chainstack文档格式构建请求
        payload = {
            "id": 1,
            "jsonrpc": "2.0",
            "method": method,
            "params": params
        }

        headers = {
            "Content-Type": "application/json"
        }

        try:
            self.stats['total_rpc_calls'] += 1

            response = self.session.post(
                self.rpc_url,
                json=payload,
                headers=headers,
                timeout=self.rpc_timeout
            )
            response.raise_for_status()

            result = response.json()
            if 'error' in result:
                error_info = result['error']
                # 如果是commitment级别不支持的错误，不记录为错误（这是正常的回退行为）
                if 'commitment below' not in str(error_info):
                    self.logger.error(f"Chainstack RPC错误: {error_info}")
                self.stats['failed_rpc_calls'] += 1
                return {}

            self.stats['successful_rpc_calls'] += 1
            return result.get('result', {})

        except requests.exceptions.RequestException as e:
            self.logger.error(f"Chainstack RPC请求失败: {e}")
            self.stats['failed_rpc_calls'] += 1
            return {}

    def _update_sol_price(self):
        """更新SOL价格（保持原脚本逻辑）"""
        try:
            current_time = time.time()
            if current_time - self.last_sol_price_update < 300:  # 5分钟更新一次
                return

            # 保存当前汇率作为备用
            backup_rate = self.sol_price_usd

            # 使用CoinGecko API获取SOL价格
            url = "https://api.coingecko.com/api/v3/simple/price?ids=solana&vs_currencies=usd"
            response = self.session.get(url, timeout=10)

            if response.status_code == 200:
                data = response.json()
                new_rate = data['solana']['usd']
                
                # 验证汇率数据有效性
                if new_rate > 0 and new_rate < 10000:  # 合理的SOL价格范围
                    self.sol_price_usd = new_rate
                    self.last_sol_price_update = current_time
                    self.logger.debug(f"SOL价格更新: ${self.sol_price_usd}")
                else:
                    self.logger.warning(f"获取到异常SOL价格: ${new_rate}，保持使用备用汇率: ${backup_rate}")
            else:
                self.logger.warning(f"获取SOL价格失败: {response.status_code}，保持使用备用汇率: ${backup_rate}")

        except Exception as e:
            self.logger.warning(f"更新SOL价格异常: {e}，保持使用当前汇率: ${self.sol_price_usd}")

    def discover_liquidity_pool_via_solanatracker(self, token_address: str) -> Optional[str]:
        """
        通过solanatracker API发现流动性池

        Args:
            token_address: 代币地址

        Returns:
            流动性池地址
        """
        try:
            self.logger.info(f"🚀 使用solanatracker API查找LP池: {token_address}")

            url = f"https://data.solanatracker.io/tokens/{token_address}"
            headers = {
                "x-api-key": "6fbf3055-d214-40c1-975d-df028d1b0614"
            }
            response = self.session.get(url, headers=headers, timeout=30)

            if response.status_code != 200:
                self.logger.error(f"solanatracker API请求失败: {response.status_code}")
                return None

            data = response.json()
            
            # 查找pools数组中market为"pumpfun-amm"或"raydium-cpmm"的对象
            pools = data.get('pools', [])
            if isinstance(pools, list):
                # 首先查找pumpfun-amm池（保持现有优先级）
                for pool in pools:
                    if isinstance(pool, dict) and pool.get('market') == 'pumpfun-amm':
                        pool_address = pool.get('poolId')
                        if pool_address:
                            self.logger.info(f"✅ 找到pumpfun-amm LP池: {pool_address}")
                            return pool_address

                # 如果没找到pumpfun-amm，再查找raydium-cpmm池
                for pool in pools:
                    if isinstance(pool, dict) and pool.get('market') == 'raydium-cpmm':
                        pool_address = pool.get('poolId')
                        if pool_address:
                            self.logger.info(f"✅ 找到raydium-cpmm LP池: {pool_address}")
                            return pool_address

            self.logger.error("未找到pumpfun-amm或raydium-cpmm市场的流动性池")
            return None

        except Exception as e:
            self.logger.error(f"solanatracker API查询异常: {e}")
            return None

    def get_pool_token_accounts_via_chainstack(self, pool_address: str, token_address: str) -> Optional[PoolReserveInfo]:
        """
        通过Chainstack获取池储备金账户（保持原脚本逻辑）

        Args:
            pool_address: 池地址
            token_address: 代币地址

        Returns:
            池储备金信息
        """
        try:
            self.logger.info(f"🔍 使用Chainstack API获取池储备金账户: {pool_address}")

            # 严格按照Chainstack文档格式调用getTokenAccountsByOwner
            # 使用processed commitment获取最新未确认数据，减少2-4秒延迟
            params = [
                pool_address,
                {"programId": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"},
                {"encoding": "jsonParsed", "commitment": "processed"}
            ]

            result = self.make_chainstack_rpc_request("getTokenAccountsByOwner", params)
            if not result or 'value' not in result:
                self.logger.error("无法获取池的代币账户")
                return None

            token_accounts = result['value']

            token_reserve_account = None
            sol_reserve_account = None

            # 查找代币和SOL的储备金账户
            for account in token_accounts:
                account_info = account.get('account', {})
                parsed_data = account_info.get('data', {}).get('parsed', {})

                if parsed_data.get('type') == 'account':
                    info = parsed_data.get('info', {})
                    mint = info.get('mint')
                    pubkey = account.get('pubkey')

                    if mint == token_address:
                        token_reserve_account = pubkey
                        self.logger.info(f"📍 找到代币储备金账户: {pubkey}")
                        self.logger.info(f"🔍 代币账户地址长度: {len(pubkey)}")
                    elif mint == "So11111111111111111111111111111111111111112":  # SOL mint
                        sol_reserve_account = pubkey
                        self.logger.info(f"📍 找到SOL储备金账户: {pubkey}")
                        self.logger.info(f"🔍 SOL账户地址长度: {len(pubkey)}")

            if token_reserve_account and sol_reserve_account:
                pool_info = PoolReserveInfo(
                    pool_address=pool_address,
                    token_reserve_account=token_reserve_account,
                    sol_reserve_account=sol_reserve_account,
                    token_mint=token_address,
                    cached_at=datetime.now().isoformat()
                )

                # 缓存池信息
                self.pool_cache[token_address] = pool_info
                self.logger.info(f"💾 已缓存池储备金信息")
                return pool_info
            else:
                # 如果标准方法失败，尝试raydium-cpmm池的查询方法
                self.logger.info("🔄 标准方法未找到储备金账户，尝试raydium-cpmm池查询...")
                return self.get_raydium_cpmm_reserve_accounts(pool_address, token_address)

        except Exception as e:
            self.logger.error(f"获取池储备金账户异常: {e}")
            self.stats['failed_rpc_calls'] += 1
            return None

    def get_raydium_cpmm_reserve_accounts(self, pool_address: str, token_address: str) -> Optional[PoolReserveInfo]:
        """
        获取raydium-cpmm池的储备金账户

        Args:
            pool_address: 池地址
            token_address: 代币地址

        Returns:
            池储备金信息
        """
        try:
            self.logger.info(f"🔍 尝试raydium-cpmm池储备金账户查询: {pool_address}")

            # 方法1: 获取池账户信息，尝试解析vault地址
            pool_account_params = [pool_address, {"encoding": "base64", "commitment": "processed"}]
            pool_result = self.make_chainstack_rpc_request("getAccountInfo", pool_account_params)

            if pool_result and 'value' in pool_result and pool_result['value']:
                account_data = pool_result['value']
                if account_data.get('data') and len(account_data['data']) > 0:
                    try:
                        import base64
                        import struct

                        # 解码base64数据
                        data_bytes = base64.b64decode(account_data['data'][0])
                        self.logger.info(f"📊 池账户数据长度: {len(data_bytes)} 字节")

                        # raydium cpmm池的基本结构（简化版本）
                        # 这些偏移量是基于raydium cpmm池的账户布局
                        if len(data_bytes) >= 200:  # 确保有足够的数据
                            # 尝试从固定偏移量读取vault地址
                            # 注意：这些偏移量可能需要根据实际的池结构调整

                            # 尝试多个可能的偏移量来查找vault地址
                            possible_offsets = [40, 72, 104, 136, 168]  # 常见的32字节对齐偏移量

                            vault_candidates = []
                            for offset in possible_offsets:
                                if offset + 32 <= len(data_bytes):
                                    vault_bytes = data_bytes[offset:offset+32]
                                    try:
                                        import base58
                                        vault_address = base58.b58encode(vault_bytes).decode('ascii')
                                        if len(vault_address) == 44:  # 有效的Solana地址长度
                                            vault_candidates.append((offset, vault_address))
                                            self.logger.info(f"🔍 偏移量 {offset}: 候选vault地址 {vault_address}")
                                    except:
                                        continue

                            # 验证候选vault地址
                            token_reserve_account = None
                            sol_reserve_account = None

                            for offset, vault_address in vault_candidates:
                                # 检查这个地址是否是有效的代币账户
                                vault_info_params = [vault_address, {"encoding": "jsonParsed", "commitment": "processed"}]
                                vault_result = self.make_chainstack_rpc_request("getAccountInfo", vault_info_params)

                                if vault_result and 'value' in vault_result and vault_result['value']:
                                    vault_data = vault_result['value']
                                    if vault_data.get('data', {}).get('parsed', {}).get('type') == 'account':
                                        vault_info = vault_data['data']['parsed']['info']
                                        mint = vault_info.get('mint')
                                        token_amount = vault_info.get('tokenAmount', {}).get('uiAmount', 0)

                                        if token_amount and token_amount > 0:
                                            if mint == token_address:
                                                token_reserve_account = vault_address
                                                self.logger.info(f"✅ 找到代币vault: {vault_address} (余额: {token_amount})")
                                            elif mint == "So11111111111111111111111111111111111111112":
                                                sol_reserve_account = vault_address
                                                self.logger.info(f"✅ 找到SOL vault: {vault_address} (余额: {token_amount})")

                            if token_reserve_account and sol_reserve_account:
                                pool_info = PoolReserveInfo(
                                    pool_address=pool_address,
                                    token_reserve_account=token_reserve_account,
                                    sol_reserve_account=sol_reserve_account,
                                    token_mint=token_address,
                                    cached_at=datetime.now().isoformat()
                                )

                                # 缓存池信息
                                self.pool_cache[token_address] = pool_info
                                self.logger.info(f"✅ 通过池账户数据解析找到储备金账户")
                                return pool_info

                    except Exception as parse_error:
                        self.logger.error(f"解析池账户数据失败: {parse_error}")

            # 方法2: 如果解析失败，尝试通过getProgramAccounts查找
            self.logger.info("🔄 池账户解析失败，尝试程序账户查询...")

            raydium_cpmm_program_id = "CPMMoo8L3F4NbTegBCKVNunggL7H1ZpdTHKxQB5qKP1C"

            # 查询所有raydium cpmm池账户
            program_accounts_params = [
                raydium_cpmm_program_id,
                {"encoding": "base64", "commitment": "processed"}
            ]

            program_result = self.make_chainstack_rpc_request("getProgramAccounts", program_accounts_params)
            if program_result:
                self.logger.info(f"🔍 找到 {len(program_result)} 个Raydium CPMM程序账户")

                # 查找匹配的池账户
                for account in program_result:
                    if account.get('pubkey') == pool_address:
                        self.logger.info(f"✅ 找到匹配的池账户: {pool_address}")
                        # 这里可以进一步解析账户数据
                        break

            self.logger.error("所有方法都未找到完整的储备金账户")
            return None

        except Exception as e:
            self.logger.error(f"raydium-cpmm储备金账户查询异常: {e}")
            return None
    
    def _find_monitor_script(self) -> Optional[Path]:
        """查找价格监控脚本"""
        # 尝试多个可能的路径
        possible_paths = [
            Path("../price monitor/chainstack_hybrid_monitor.py"),
            Path("../../price monitor/chainstack_hybrid_monitor.py"),
            Path("chainstack_hybrid_monitor.py"),
            Path("scripts/chainstack_hybrid_monitor.py")
        ]
        
        for path in possible_paths:
            if path.exists():
                self.logger.info(f"✅ 找到监控脚本: {path}")
                return path.resolve()
        
        self.logger.warning("⚠️ 未找到价格监控脚本")
        return None
    
    def add_price_callback(self, callback: Callable[[str, PriceData], None]) -> None:
        """添加价格更新回调"""
        self.price_callbacks.append(callback)
        self.logger.debug(f"添加价格回调，当前回调数: {len(self.price_callbacks)}")
    
    def remove_price_callback(self, callback: Callable[[str, PriceData], None]) -> None:
        """移除价格更新回调"""
        if callback in self.price_callbacks:
            self.price_callbacks.remove(callback)
            self.logger.debug(f"移除价格回调，当前回调数: {len(self.price_callbacks)}")
    
    async def start_monitoring(self, token_address: str) -> bool:
        """开始监控代币价格（集成原脚本完整逻辑）"""
        if token_address in self.monitoring_tokens and self.monitoring_tokens[token_address]:
            self.logger.warning(f"代币 {token_address[:8]} 已在监控中")
            return True

        try:
            self.logger.info(f"🚀 开始Chainstack混合策略价格监控")
            self.logger.info(f"📍 代币地址: {token_address}")
            self.logger.info(f"⏱️ 监控间隔: {self.update_interval}秒")
            self.logger.info(f"🌐 使用代理: {'是' if self.use_proxy else '否'}")

            # 检查缓存（保持原脚本逻辑）
            if token_address in self.pool_cache:
                self.logger.info("✅ 从缓存获取池信息")
                pool_info = self.pool_cache[token_address]
                self.stats['cache_hits'] += 1
            else:
                # 步骤一：发现流动性池
                self.logger.info("📡 步骤一：使用solanatracker API发现流动性池...")
                pool_address = self.discover_liquidity_pool_via_solanatracker(token_address)
                if not pool_address:
                    self.logger.error("❌ 无法发现流动性池，监控失败")
                    return False

                # 步骤二：获取储备金账户
                self.logger.info("🔍 步骤二：使用Chainstack获取储备金账户...")
                pool_info = self.get_pool_token_accounts_via_chainstack(pool_address, token_address)
                if not pool_info:
                    self.logger.error("❌ 无法获取储备金账户，监控失败")
                    return False

                self.stats['cache_misses'] += 1

            self.logger.info("✅ 池信息准备完成，开始高频监控...")
            self.logger.info("🔧 使用纯RPC轮询模式（节省API调用，保持实时性）...")

            # 开始监控
            self.monitoring_tokens[token_address] = True
            self.price_data[token_address] = []
            if not self.stats['start_time']:
                self.stats['start_time'] = datetime.now()

            # 启动监控线程（使用原脚本的监控逻辑）
            monitor_thread = threading.Thread(
                target=self._integrated_monitoring_loop,
                args=(token_address, pool_info, self.update_interval),
                daemon=True
            )
            monitor_thread.start()
            self.monitor_threads[token_address] = monitor_thread

            self.logger.info("🎯 Chainstack混合策略监控已启动")
            return True

        except Exception as e:
            self.logger.error(f"启动价格监控失败 {token_address[:8]}: {e}")
            return False
    
    async def stop_monitoring(self, token_address: str) -> bool:
        """停止监控代币价格"""
        if token_address not in self.monitoring_tokens:
            return True

        try:
            # 停止监控
            self.monitoring_tokens[token_address] = False

            # 终止进程
            if token_address in self.monitor_processes:
                process = self.monitor_processes[token_address]
                if process.poll() is None:  # 进程还在运行
                    process.terminate()
                    try:
                        process.wait(timeout=5)
                    except subprocess.TimeoutExpired:
                        process.kill()
                del self.monitor_processes[token_address]

            # 清理线程引用
            if token_address in self.monitor_threads:
                del self.monitor_threads[token_address]



            self.logger.info(f"📉 停止监控代币价格: {token_address[:8]}")
            return True

        except Exception as e:
            self.logger.error(f"停止价格监控失败 {token_address[:8]}: {e}")
            return False


    
    def _monitor_token_thread(self, token_address: str) -> None:
        """监控代币的线程函数"""
        try:
            if self.script_path and self.script_path.exists():
                self._monitor_with_script(token_address)
            else:
                self._monitor_with_simulation(token_address)
        except Exception as e:
            self.logger.error(f"监控线程异常 {token_address[:8]}: {e}")
    
    def _monitor_with_script(self, token_address: str) -> None:
        """使用外部脚本监控"""
        output_file = self.temp_dir / f"price_{token_address[:8]}.json"
        
        try:
            # 构建命令
            cmd = [
                "python", 
                str(self.script_path),
                "--token", token_address,
                "--output", str(output_file),
                "--interval", str(self.update_interval)
            ]
            
            # 启动监控进程
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            self.monitor_processes[token_address] = process
            
            self.logger.info(f"🔧 启动外部监控进程: {token_address[:8]}")
            
            # 监控输出文件
            last_modified = 0
            while self.monitoring_tokens.get(token_address, False):
                try:
                    if output_file.exists():
                        current_modified = output_file.stat().st_mtime
                        if current_modified > last_modified:
                            last_modified = current_modified
                            
                            # 读取价格数据
                            with open(output_file, 'r') as f:
                                data = json.load(f)
                                
                            price_data = PriceData(
                                timestamp=data.get("timestamp", time.time()),
                                price=float(data.get("price", 0)),
                                volume=float(data.get("volume", 0)),
                                source="chainstack"
                            )
                            
                            self._process_price_data(token_address, price_data)
                    
                    time.sleep(0.1)  # 短暂休眠
                    
                except Exception as e:
                    self.logger.error(f"读取价格数据失败 {token_address[:8]}: {e}")
                    time.sleep(1)
            
        except Exception as e:
            self.logger.error(f"外部脚本监控失败 {token_address[:8]}: {e}")
        finally:
            # 清理输出文件
            if output_file.exists():
                try:
                    output_file.unlink()
                except Exception as e:
                    self.logger.debug(f"读取价格数据失败: {e}")
    
    def _monitor_with_simulation(self, token_address: str) -> None:
        """模拟价格监控（用于测试）"""
        self.logger.warning(f"⚠️ 使用模拟价格数据: {token_address[:8]}")
        
        # 模拟价格数据
        base_price = 0.000085  # 基础价格
        price = base_price
        
        while self.monitoring_tokens.get(token_address, False):
            try:
                # 模拟价格波动 (-2% 到 +2%)
                import random
                change_percent = random.uniform(-0.02, 0.02)
                price = price * (1 + change_percent)
                
                # 确保价格在合理范围内
                price = max(0.000001, min(0.001, price))
                
                price_data = PriceData(
                    timestamp=time.time(),
                    price=price,
                    volume=random.uniform(1000, 10000),
                    source="simulation"
                )
                
                self._process_price_data(token_address, price_data)
                
                time.sleep(self.update_interval)
                
            except Exception as e:
                self.logger.error(f"模拟价格生成失败 {token_address[:8]}: {e}")
                time.sleep(1)
    
    def _process_price_data(self, token_address: str, price_data: PriceData) -> None:
        """处理价格数据"""
        try:
            # 存储价格数据
            if token_address not in self.price_data:
                self.price_data[token_address] = []

            self.price_data[token_address].append(price_data)

            # 保持历史数据在限制范围内
            if len(self.price_data[token_address]) > self.history_length:
                self.price_data[token_address] = self.price_data[token_address][-self.history_length:]


            
            # 调用回调函数 - 传递USD价格给策略引擎
            for callback in self.price_callbacks:
                try:
                    # 验证汇率有效性
                    if self.sol_price_usd <= 0:
                        self.logger.warning(f"SOL/USD汇率无效: {self.sol_price_usd}，跳过价格转换")
                        continue
                    
                    # 创建USD价格数据对象传递给策略引擎
                    usd_price = price_data.price * self.sol_price_usd
                    
                    # 验证转换后价格的合理性
                    if usd_price <= 0 or usd_price > 1.0:  # USD价格应在合理范围内
                        self.logger.warning(f"转换后USD价格异常: {usd_price:.8f}，SOL价格: {price_data.price:.8f}，汇率: {self.sol_price_usd}")
                        continue
                    
                    usd_price_data = PriceData(
                        timestamp=price_data.timestamp,
                        price=usd_price,  # 转换为USD价格
                        volume=price_data.volume,
                        source=price_data.source
                    )
                    callback(token_address, usd_price_data)
                except Exception as e:
                    self.logger.error(f"价格回调执行失败: {e}")
            
            # 显示价格信息（仿照原始脚本的显示格式）
            price_count = len(self.price_data[token_address])
            if price_count >= 2:
                # 计算价格变化
                prev_price = self.price_data[token_address][-2].price
                current_price = price_data.price
                change_pct = ((current_price - prev_price) / prev_price) * 100 if prev_price > 0 else 0
                change_absolute = current_price - prev_price

                # 每5次更新显示一次详细信息（约2秒）
                if price_count % 5 == 0:
                    trend_symbol = "+" if change_pct > 0 else "-" if change_pct < 0 else "="

                    # 仿照原始脚本的显示格式
                    print(f"\n{'='*80}")
                    print(f"时间: {price_data.timestamp}")
                    print(f"代币: {token_address[:8]}...")
                    print(f"价格: {current_price:.8f} SOL (${current_price * self.sol_price_usd:.6f} USD)")
                    print(f"变化: {change_pct:+.2f}% ({change_absolute:+.8f} SOL)")

                    # MA数据显示 - 在数据点足够后启用
                    if price_count >= 45:  # 45个数据点后开始显示MA（约20秒后）
                        ma_info = self._calculate_ma_data(token_address)
                        if ma_info:
                            fast_ma_usd = ma_info['fast_ma'] * self.sol_price_usd
                            slow_ma_usd = ma_info['slow_ma'] * self.sol_price_usd
                            trend_status = "上升趋势" if ma_info['breakout'] else "下降趋势"
                            trend_indicator = "UP" if ma_info['breakout'] else "DOWN"
                            print(f"快MA(6s): {ma_info['fast_ma']:.8f} SOL (${fast_ma_usd:.6f} USD)")
                            print(f"慢MA(17s): {ma_info['slow_ma']:.8f} SOL (${slow_ma_usd:.6f} USD)")
                            print(f"MA状态: {trend_status} [{trend_indicator}]")
                        else:
                            print(f"MA数据: 计算中... (需要45个数据点)")
                    else:
                        print(f"MA数据: 积累中... ({price_count}/45个数据点)")

                    # 电报验证状态显示
                    telegram_status = self._get_telegram_status(token_address)
                    if telegram_status:
                        print(f"电报状态: {telegram_status}")
                    # print(f"数据源: chainstack_hybrid_rpc | 数据点: {price_count}")  # 已注释：避免20秒后固定显示60造成误导
                    print(f"RPC成功率: {(self.stats['successful_rpc_calls']/(self.stats['total_rpc_calls'] or 1)*100):.1f}%")
                    print(f"{'='*80}")
            else:
                # 首次价格数据
                print(f"\n{token_address[:8]} | 首次价格: {price_data.price:.8f} SOL")
                
        except Exception as e:
            self.logger.error(f"处理价格数据失败 {token_address[:8]}: {e}")



    def _get_ma_info(self, token_address: str) -> Optional[dict]:
        """
        获取MA信息

        Args:
            token_address: 代币地址

        Returns:
            MA信息字典，如果无法获取则返回None
        """
        try:
            # 通过回调获取策略引擎的MA数据
            if hasattr(self, 'strategy_callback') and self.strategy_callback:
                return self.strategy_callback(token_address)
            return None
        except Exception as e:
            self.logger.debug(f"获取MA信息失败: {e}")
            return None

    def _calculate_ma_data(self, token_address: str) -> Optional[dict]:
        """
        直接计算MA数据（用于价格监控显示）

        Args:
            token_address: 代币地址

        Returns:
            MA信息字典，包含fast_ma, slow_ma, breakout
        """
        try:
            if token_address not in self.price_data:
                return None

            price_history = self.price_data[token_address]
            if len(price_history) < 45:  # 需要至少45个数据点计算慢MA
                return None

            # 提取价格值
            prices = [data.price for data in price_history]

            # 计算快MA（15个数据点，6秒）
            fast_ma = sum(prices[-15:]) / 15 if len(prices) >= 15 else None

            # 计算慢MA（45个数据点，17秒）
            slow_ma = sum(prices[-45:]) / 45 if len(prices) >= 45 else None

            if fast_ma is None or slow_ma is None:
                return None

            # 判断突破状态
            breakout = fast_ma > slow_ma

            return {
                'fast_ma': fast_ma,
                'slow_ma': slow_ma,
                'breakout': breakout
            }

        except Exception as e:
            self.logger.debug(f"计算MA数据失败 {token_address[:8]}: {e}")
            return None

    def set_strategy_callback(self, callback):
        """设置策略回调函数"""
        self.strategy_callback = callback

    async def get_latest_price(self, token_address: str) -> Optional[PriceData]:
        """获取最新价格"""
        if token_address not in self.price_data or not self.price_data[token_address]:
            return None
        
        return self.price_data[token_address][-1]
    
    async def get_price_history(self, token_address: str, count: int) -> List[PriceData]:
        """获取价格历史"""
        if token_address not in self.price_data:
            return []
        
        history = self.price_data[token_address]
        if count <= 0:
            return history.copy()
        
        return history[-count:] if len(history) >= count else history.copy()
    
    def get_monitoring_status(self) -> Dict[str, Any]:
        """获取监控状态"""
        status = {
            "active_tokens": len(self.monitoring_tokens),
            "total_callbacks": len(self.price_callbacks),
            "tokens": {}
        }
        
        for token_address, is_monitoring in self.monitoring_tokens.items():
            latest_price = None
            data_count = 0
            
            if token_address in self.price_data:
                data_count = len(self.price_data[token_address])
                if data_count > 0:
                    latest_price = self.price_data[token_address][-1].price
            
            status["tokens"][token_address] = {
                "monitoring": is_monitoring,
                "latest_price": latest_price,
                "data_points": data_count,
                "has_process": token_address in self.monitor_processes
            }
        
        return status
    
    async def cleanup(self) -> None:
        """清理资源"""
        self.logger.info("🧹 清理价格监控器资源...")

        # 停止所有监控
        tokens_to_stop = list(self.monitoring_tokens.keys())
        for token_address in tokens_to_stop:
            await self.stop_monitoring(token_address)



        # 清理临时文件
        try:
            for file in self.temp_dir.glob("price_*.json"):
                file.unlink()
        except Exception as e:
            self.logger.warning(f"清理临时文件失败: {e}")

        self.logger.info("✅ 价格监控器资源清理完成")



    def _get_telegram_status(self, token_address: str) -> Optional[str]:
        """
        获取代币的电报验证状态

        Args:
            token_address: 代币地址

        Returns:
            状态描述字符串
        """
        try:
            # 通过回调获取代币实例状态
            if hasattr(self, 'telegram_status_callback') and self.telegram_status_callback:
                return self.telegram_status_callback(token_address)
            return None
        except Exception as e:
            self.logger.debug(f"获取电报状态失败: {e}")
            return None

    def _integrated_monitoring_loop(self, token_address: str, pool_info: PoolReserveInfo, interval: float):
        """
        集成的监控循环（保持原脚本的完整逻辑）

        Args:
            token_address: 代币地址
            pool_info: 池信息
            interval: 监控间隔
        """
        try:
            self.logger.info(f"🔄 开始监控循环: {token_address[:8]}")
            self.logger.info(f"🔍 监控状态检查: {self.monitoring_tokens.get(token_address, False)}")
            while self.monitoring_tokens.get(token_address, False):
                start_time = time.time()

                # 获取储备金数据（保持原脚本逻辑）
                reserves = self._get_reserves_via_rpc(pool_info)
                self.logger.debug(f"🔍 RPC调用结果: {reserves is not None}")
                if reserves:
                    self.logger.debug(f"📊 储备金详情: {reserves}")
                else:
                    self.logger.debug(f"❌ 储备金为空或None")

                if reserves:
                    # 计算价格（保持原脚本逻辑）
                    price_data = self._calculate_price(reserves, token_address)
                    self.logger.debug(f"💰 价格计算结果: {price_data is not None}")

                    if price_data:
                        # 使用统一的价格数据处理方法（包含显示逻辑）
                        self._process_price_data(token_address, price_data)

                        # 记录价格历史（保持原脚本格式）
                        snapshot = PriceSnapshot(
                            price_sol=price_data.price,
                            price_usd=price_data.price * self.sol_price_usd,
                            timestamp=datetime.now().isoformat(),
                            token_reserve=reserves.get('token_reserve', 0),
                            sol_reserve=reserves.get('sol_reserve', 0),
                            source="chainstack_hybrid_rpc"
                        )
                        self.price_history.append(snapshot)

                        # 保持历史记录在合理范围内（防止内存泄漏）
                        if len(self.price_history) > 500:  # 降低阈值，更频繁清理
                            self.price_history = self.price_history[-250:]  # 保留最近250条

                # 控制监控间隔（保持原脚本的精确时间控制）
                elapsed = time.time() - start_time
                sleep_time = max(0, interval - elapsed)
                if sleep_time > 0:
                    time.sleep(sleep_time)

        except Exception as e:
            self.logger.error(f"监控循环异常 {token_address[:8]}: {e}")
        finally:
            self.monitoring_tokens[token_address] = False

    def _get_reserves_via_rpc(self, pool_info: PoolReserveInfo) -> Optional[Dict[str, float]]:
        """
        使用RPC获取储备金数据（使用原脚本的方法）
        """
        try:
            # 使用原脚本的并行获取方法
            import concurrent.futures

            with concurrent.futures.ThreadPoolExecutor(max_workers=2) as executor:
                # 同时发起两个RPC请求（与原脚本保持一致）
                token_future = executor.submit(self._get_token_account_balance, pool_info.token_reserve_account)
                sol_future = executor.submit(self._get_token_account_balance, pool_info.sol_reserve_account)

                # 等待两个请求完成
                token_reserve = token_future.result(timeout=6.0)
                sol_reserve = sol_future.result(timeout=6.0)

            if token_reserve is None or sol_reserve is None:
                self.logger.warning("无法获取储备金余额")
                return None

            if token_reserve <= 0 or sol_reserve <= 0:
                self.logger.warning("储备金余额无效")
                return None

            return {
                'token_reserve': token_reserve,
                'sol_reserve': sol_reserve,
                'timestamp': time.time()
            }

        except Exception as e:
            self.logger.error(f"获取储备金数据异常: {e}")
            self.stats['failed_rpc_calls'] += 1
            return None

    def _get_token_account_balance(self, account_address: str) -> Optional[float]:
        """
        使用Chainstack getTokenAccountBalance获取账户余额（与原脚本保持一致）
        """
        try:
            # 严格按照原脚本的格式调用getTokenAccountBalance
            params = [account_address, {"commitment": "processed"}]

            payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "getTokenAccountBalance",
                "params": params
            }

            response = self.session.post(self.rpc_url, json=payload, timeout=self.rpc_timeout)
            self.stats['total_rpc_calls'] += 1

            if response.status_code != 200:
                self.stats['failed_rpc_calls'] += 1
                # 如果processed不支持，回退到confirmed
                params = [account_address, {"commitment": "confirmed"}]
                payload["params"] = params
                response = self.session.post(self.rpc_url, json=payload, timeout=self.rpc_timeout)

                if response.status_code != 200:
                    return None

            data = response.json()

            if 'error' in data:
                self.stats['failed_rpc_calls'] += 1
                # 如果processed不支持，回退到confirmed
                params = [account_address, {"commitment": "confirmed"}]
                payload["params"] = params
                response = self.session.post(self.rpc_url, json=payload, timeout=self.rpc_timeout)

                if response.status_code != 200:
                    return None

                data = response.json()
                if 'error' in data:
                    return None

            self.stats['successful_rpc_calls'] += 1

            result = data.get('result')
            if result and 'value' in result:
                ui_amount = result['value'].get('uiAmount')
                if ui_amount is not None:
                    return float(ui_amount)

            return None

        except Exception as e:
            self.logger.debug(f"获取账户余额失败: {e}")
            return None

    def _calculate_price(self, reserves: Dict[str, float], token_address: str) -> Optional[PriceData]:
        """
        计算代币价格（保持原脚本逻辑）

        Args:
            reserves: 储备金数据
            token_address: 代币地址

        Returns:
            价格数据
        """
        try:
            token_reserve = reserves.get('token_reserve', 0)
            sol_reserve = reserves.get('sol_reserve', 0)

            self.logger.debug(f"储备金数据: TOKEN={token_reserve}, SOL={sol_reserve}")

            if token_reserve <= 0 or sol_reserve <= 0:
                self.logger.debug(f"储备金无效: TOKEN={token_reserve}, SOL={sol_reserve}")
                return None

            # 计算价格: price = sol_reserve / token_reserve（保持原脚本公式）
            price_sol = sol_reserve / token_reserve
            price_usd = price_sol * self.sol_price_usd

            # 获取MA状态信息（如果数据足够）
            ma_status_info = ""
            if token_address in self.price_data and len(self.price_data[token_address]) >= 45:
                ma_info = self._calculate_ma_data(token_address)
                if ma_info:
                    fast_ma_usd = ma_info['fast_ma'] * self.sol_price_usd
                    slow_ma_usd = ma_info['slow_ma'] * self.sol_price_usd
                    trend_status = "上升趋势" if ma_info['breakout'] else "下降趋势"
                    ma_status_info = f" | MA: 快={ma_info['fast_ma']:.8f}SOL(${fast_ma_usd:.8f}USD), 慢={ma_info['slow_ma']:.8f}SOL(${slow_ma_usd:.8f}USD), 状态={trend_status}"
                else:
                    ma_status_info = f" | MA: 计算中... (数据点: {len(self.price_data[token_address])})"
            elif token_address in self.price_data:
                ma_status_info = f" | MA: 积累中... ({len(self.price_data[token_address])}/45个数据点)"
            else:
                ma_status_info = " | MA: 等待数据..."

            self.logger.debug(f"计算价格: {price_sol:.8f} SOL (${price_usd:.8f} USD){ma_status_info}")

            # 更新SOL价格
            self._update_sol_price()

            # 创建PriceData对象
            price_data = PriceData(
                timestamp=reserves.get('timestamp', time.time()),
                price=price_sol,
                volume=sol_reserve  # 使用SOL储备金作为成交量指标
            )

            return price_data

        except Exception as e:
            self.logger.debug(f"计算价格异常: {e}")
            return None


def create_price_monitor() -> IPriceMonitor:
    """创建价格监控器实例"""
    config_manager = get_config_manager()
    price_config = config_manager.get_price_monitor_config()
    
    return ChainStackPriceMonitor(price_config)
